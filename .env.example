# Google Cloud Authentication
# Authentication Method 1: Service Account Key File (Recommended)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Authentication Method 2: Environment Variables
# GOOGLE_CLIENT_EMAIL=<EMAIL>
# GOOGLE_PRIVATE_KEY=your-private-key

# IMPORTANT: Google Cloud Project ID
# This is required for most operations. If not set, the server will attempt to
# extract it from your credentials file, but it's recommended to set it explicitly.
GOOGLE_CLOUD_PROJECT=your-project-id

# Enable debug logging
# DEBUG=true

# Enable lazy authentication loading (helps with Smith<PERSON>)
# LAZY_AUTH=true