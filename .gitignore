# Rules
.windsurfrules

# Dependencies
node_modules/
.pnpm-store/

# Build output
dist/
build/

# Environment variables
.env
.env.local
.env.*.local

# Logs
*.log
npm-debug.log*
pnpm-debug.log*

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
.DS_Store
Thumbs.db

# Service account keys
*-key.json
*-credentials.json

# Test coverage
coverage/

logs
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
.idea
.vscode
# OS specific

# Task files
# tasks.json
# tasks/ 
