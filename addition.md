Of course. Here are the step-by-step instructions to integrate the powerful, generic `run-gcp-code` tool from the `gcp-mcp` project into the more comprehensive `google-cloud-mcp` project.

This will give you the best of both worlds: the deep, specialized tools and resources of `google-cloud-mcp` combined with the flexibility to interact with any GCP service programmatically.

---

### **Goal: Add the `run-gcp-code` Tool to `google-cloud-mcp`**

We will add the necessary dependencies and code to the `google-cloud-mcp` project, making sure to use its existing robust authentication and state management utilities.

#### **Step 1: Update Dependencies in `package.json`**

First, we need to add the required packages for code execution and for the additional GCP client libraries that the tool will make available.

1.  Open the `package.json` file in the `google-cloud-mcp` project.
2.  Add the following packages to the `"dependencies"` section. You can place them alphabetically among the existing `@google-cloud/*` packages.

    ```json
    "@google-cloud/bigquery": "^7.9.2",
    "@google-cloud/billing": "^4.6.0",
    "@google-cloud/billing-budgets": "^4.3.0",
    "@google-cloud/compute": "^4.12.0",
    "@google-cloud/container": "^5.19.0",
    "@google-cloud/functions": "^3.6.1",
    "@google-cloud/resource-manager": "^5.3.1",
    "@google-cloud/run": "^1.5.1",
    "@google-cloud/sql": "^0.19.1",
    "@google-cloud/storage": "^7.15.0",
    "ts-morph": "^24.0.0"
    ```

3.  After saving `package.json`, run the following command in your terminal at the root of the `google-cloud-mcp` project to install the new dependencies:

    ```bash
    pnpm install
    ```

#### **Step 2: Create the Generic Code Runner Tool File**

To keep the project organized, we'll create a new file specifically for this generic tool.

1.  Create a new directory inside `src/services/` named `generic`.
2.  Inside this new `src/services/generic/` directory, create a file named `tools.ts`.

#### **Step 3: Add the Tool's Logic**

Copy and paste the following code into the newly created `src/services/generic/tools.ts` file.

This code defines the `run-gcp-code` tool, creates a secure sandbox for execution, and populates it with a comprehensive set of GCP client libraries. It intelligently uses the existing `getProjectId` utility to ensure it's always acting on the correct project.

```typescript
// src/services/generic/tools.ts

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';
import { Project, SyntaxKind } from 'ts-morph';
import { createContext, runInContext } from 'vm';
import { getProjectId } from '../../utils/auth.js';
import { GcpMcpError } from '../../utils/error.js';
import { logger } from '../../utils/logger.js';

// Import all necessary GCP clients
import { InstancesClient } from '@google-cloud/compute';
import { Storage } from '@google-cloud/storage';
import { CloudFunctionsServiceClient } from '@google-cloud/functions';
import { ServicesClient } from '@google-cloud/run';
import { BigQuery } from '@google-cloud/bigquery';
import { ProjectsClient } from '@google-cloud/resource-manager';
import { CloudBillingClient } from '@google-cloud/billing';
import { BudgetServiceClient } from '@google-cloud/billing-budgets';
import { ClusterManagerClient } from '@google-cloud/container';
import { Logging } from '@google-cloud/logging';
import { SqlInstancesServiceClient } from '@google-cloud/sql';
import { MetricServiceClient } from '@google-cloud/monitoring';
import { Spanner } from '@google-cloud/spanner';

// This is the instructional prompt for the AI, explaining how to write the code.
const codePrompt = `Your job is to answer questions about the user's GCP environment by writing Javascript/TypeScript code using Google Cloud Client Libraries. The code must adhere to a few rules:
- Must be a single block of code that uses async/await.
- Must be written in TypeScript using the official Google Cloud client libraries provided in the context.
- Avoid hardcoded values like project IDs; use the 'projectId' variable available in the context.
- Code should handle errors gracefully.
- Data returned from GCP APIs must be returned as a JSON object containing only the minimal amount of data needed to answer the question.
- The code MUST return a value (e.g., string, number, boolean, or JSON object). If nothing is returned, the operation will be considered a failure.
- Do not include comments in the code.
- Use the 'help()' function in the context to see all available clients and examples.`;

/**
 * Wraps user-provided code to ensure the last statement is a return statement.
 * This is crucial for getting output from the sandboxed vm.
 * @param userCode The code string provided by the AI.
 * @returns The transformed code with an explicit return.
 */
function wrapUserCode(userCode: string): string {
  const project = new Project({ useInMemoryFileSystem: true });
  const sourceFile = project.createSourceFile("userCode.ts", userCode);
  const lastStatement = sourceFile.getStatements().pop();

  if (lastStatement && lastStatement.getKind() === SyntaxKind.ExpressionStatement) {
    const returnStatement = lastStatement.asKindOrThrow(SyntaxKind.ExpressionStatement);
    const expression = returnStatement.getExpression();
    // Replace the last expression statement with a return statement
    returnStatement.replaceWithText(`return ${expression.getText()};`);
  }

  return sourceFile.getFullText();
}

/**
 * Registers the generic `run-gcp-code` tool with the MCP server.
 * @param server The MCP server instance.
 */
export function registerGenericTool(server: McpServer): void {
  server.tool(
    'run-gcp-code',
    {
      reasoning: z.string().describe("The reasoning behind the code you are about to write."),
      code: z.string().describe(codePrompt),
    },
    async ({ reasoning, code }, _extra) => {
      try {
        const projectId = await getProjectId();
        logger.info(`Executing generic code for project: ${projectId}. Reasoning: ${reasoning}`);

        // Documentation for available clients that the AI can access via help()
        const gcpClientDocs = `
Available clients in the execution context:
- compute: new InstancesClient({ projectId })
- storage: new Storage({ projectId })
- functions: new CloudFunctionsServiceClient({ projectId })
- run: new ServicesClient({ projectId })
- bigquery: new BigQuery({ projectId })
- resourceManager: new ProjectsClient({ projectId })
- billing: new CloudBillingClient()
- budgets: new BudgetServiceClient()
- container: new ClusterManagerClient()
- logging: new Logging({ projectId })
- sql: new SqlInstancesServiceClient()
- monitoring: new MetricServiceClient()
- spanner: new Spanner({ projectId })

Example: To list all GCS buckets, write the following code:
const [buckets] = await storage.getBuckets();
return buckets.map(b => b.name);
`;

        // Create a secure sandbox with all available clients
        const context = {
          projectId,
          // GCP Clients
          compute: new InstancesClient({ projectId }),
          storage: new Storage({ projectId }),
          functions: new CloudFunctionsServiceClient({ projectId }),
          run: new ServicesClient({ projectId }),
          bigquery: new BigQuery({ projectId }),
          resourceManager: new ProjectsClient({ projectId }),
          billing: new CloudBillingClient(),
          budgets: new BudgetServiceClient(),
          container: new ClusterManagerClient(),
          logging: new Logging({ projectId }),
          sql: new SqlInstancesServiceClient(),
          monitoring: new MetricServiceClient(),
          spanner: new Spanner({ projectId }),
          // Helper function
          help: () => gcpClientDocs,
        };

        const wrappedCode = wrapUserCode(code);
        // Wrap in an async IIFE to support top-level await
        const fullCode = `(async () => { ${wrappedCode} })();`;
        
        const result = await runInContext(fullCode, createContext(context));

        logger.debug(`Generic code execution successful. Result: ${JSON.stringify(result)}`);

        return {
          content: [{
            type: 'text',
            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
          }],
        };
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger.error(`Error executing generic GCP code: ${errorMessage}`);
        throw new GcpMcpError(
          `Error executing GCP code: ${errorMessage}`,
          'EXECUTION_FAILED',
          500
        );
      }
    }
  );
}
```

#### **Step 4: Register the New Tool**

Now, we need to tell the main server to load and register this new tool.

1.  Open the main server file: `src/index.ts`.
2.  Add the following import statement at the top with the other service imports:

    ```typescript
    import { registerGenericTool } from './services/generic/tools.js';
    ```

3.  Inside the `main` function, find the block where other services are registered (e.g., `registerLoggingResources(server);`). Add the call to register your new generic tool. It's best to place it with the other tool registrations.

    ```typescript
    // Inside the main() function in src/index.ts

    try {
      // Register additional tools
      logger.info('Registering additional tools');
      registerProjectTools(server);
      registerGenericTool(server); // <-- ADD THIS LINE
    } catch (error) {
      logger.warn(`Error registering project tools: ${error instanceof Error ? error.message : String(error)}`);
    }
    ```

#### **Step 5: Build and Run**

Finally, rebuild the project to include your changes and start the server.

1.  Run the build command from the project root:

    ```bash
    pnpm build
    ```

2.  Start the server:
    ```bash
    pnpm start
    ```

---

### **Verification**

Your `google-cloud-mcp` server is now equipped with the `run-gcp-code` tool. An AI assistant connected to it can now perform a much wider range of tasks.

For example, you could now ask your AI assistant:

> "Using the `run-gcp-code` tool, write and execute code to list all of my Google Cloud Storage buckets."

The assistant should be able to use the provided `codePrompt` and `help()` function to generate and run the correct TypeScript code, giving you the list of your GCS buckets.