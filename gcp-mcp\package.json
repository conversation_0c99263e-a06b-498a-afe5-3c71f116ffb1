{"name": "gcp-mcp", "version": "1.0.2", "description": "Model Context Protocol (MCP) server for Google Cloud Platform - Talk to your GCP resources", "main": "index.ts", "bin": {"gcp-mcp": "./bin.js"}, "scripts": {"start": "tsx index.ts", "prepare": "npm run build", "build": "tsc", "dev": "tsx watch index.ts", "test": "tsx bin.js"}, "keywords": ["gcp", "mcp", "claude", "ai", "llm"], "author": "<PERSON>lu<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@google-cloud/bigquery": "^7.9.2", "@google-cloud/billing": "^4.6.0", "@google-cloud/billing-budgets": "^4.3.0", "@google-cloud/compute": "^4.12.0", "@google-cloud/container": "^5.19.0", "@google-cloud/functions": "^3.6.1", "@google-cloud/logging": "^11.2.0", "@google-cloud/resource-manager": "^5.3.1", "@google-cloud/run": "^1.5.1", "@google-cloud/sql": "^0.19.1", "@google-cloud/storage": "^7.15.0", "@modelcontextprotocol/sdk": "^1.6.1", "google-auth-library": "^9.0.0", "googleapis": "^146.0.0", "ts-morph": "^24.0.0", "tsx": "^4.19.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.10.2", "tslib": "^2.8.1", "typescript": "^5.7.2"}, "repository": {"type": "git", "url": "git+https://github.com/eniayomi/gcp-mcp.git"}, "bugs": {"url": "https://github.com/eniayomi/gcp-mcp/issues"}, "homepage": "https://github.com/eniayomi/gcp-mcp#readme"}