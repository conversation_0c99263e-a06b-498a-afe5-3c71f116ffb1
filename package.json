{"name": "google-cloud-mcp", "version": "0.1.4", "description": "Model Context Protocol server for Google Cloud services", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc && pnpm run copy-assets", "copy-assets": "cp -R src/services/monitoring/*.md dist/services/monitoring/", "start": "node dist/index.js", "dev": "ts-node --esm src/index.ts", "test": "vitest run", "test:watch": "vitest", "lint": "eslint src --ext .ts", "format": "prettier --write \"src/**/*.ts\""}, "keywords": ["mcp", "model-context-protocol", "google-cloud", "spanner", "logging", "monitoring", "trace"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@google-cloud/bigquery": "^7.9.2", "@google-cloud/billing": "^4.6.0", "@google-cloud/billing-budgets": "^4.3.0", "@google-cloud/compute": "^4.12.0", "@google-cloud/container": "^5.19.0", "@google-cloud/functions": "^3.6.1", "@google-cloud/logging": "^11.2.0", "@google-cloud/monitoring": "^4.1.0", "@google-cloud/resource-manager": "^5.3.1", "@google-cloud/run": "^1.5.1", "@google-cloud/spanner": "^7.18.1", "@google-cloud/sql": "^0.19.1", "@google-cloud/storage": "^7.15.0", "@google-cloud/trace-agent": "^8.0.0", "@modelcontextprotocol/sdk": "^1.6.1", "dotenv": "^16.4.7", "google-auth-library": "^9.15.1", "ts-morph": "^24.0.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.13.9", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.2", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.2", "vitest": "^1.6.1"}, "engines": {"node": ">=18.0.0"}}