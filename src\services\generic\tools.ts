// src/services/generic/tools.ts

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';
import { Project, SyntaxKind } from 'ts-morph';
import { createContext, runInContext } from 'vm';
import { getProjectId } from '../../utils/auth.js';
import { GcpMcpError } from '../../utils/error.js';
import { logger } from '../../utils/logger.js';

// Import all necessary GCP clients
import { InstancesClient } from '@google-cloud/compute';
import { Storage } from '@google-cloud/storage';
import { CloudFunctionsServiceClient } from '@google-cloud/functions';
import { ServicesClient } from '@google-cloud/run';
import { BigQuery } from '@google-cloud/bigquery';
import { ProjectsClient } from '@google-cloud/resource-manager';
import { CloudBillingClient } from '@google-cloud/billing';
import { BudgetServiceClient } from '@google-cloud/billing-budgets';
import { ClusterManagerClient } from '@google-cloud/container';
import { Logging } from '@google-cloud/logging';
import { SqlInstancesServiceClient } from '@google-cloud/sql';
import { MetricServiceClient } from '@google-cloud/monitoring';
import { Spanner } from '@google-cloud/spanner';

// This is the instructional prompt for the AI, explaining how to write the code.
const codePrompt = `Your job is to answer questions about the user's GCP environment by writing Javascript/TypeScript code using Google Cloud Client Libraries. The code must adhere to a few rules:
- Must be a single block of code that uses async/await.
- Must be written in TypeScript using the official Google Cloud client libraries provided in the context.
- Avoid hardcoded values like project IDs; use the 'projectId' variable available in the context.
- Code should handle errors gracefully.
- Data returned from GCP APIs must be returned as a JSON object containing only the minimal amount of data needed to answer the question.
- The code MUST return a value (e.g., string, number, boolean, or JSON object). If nothing is returned, the operation will be considered a failure.
- Do not include comments in the code.
- Use the 'help()' function in the context to see all available clients and examples.`;

/**
 * Wraps user-provided code to ensure the last statement is a return statement.
 * This is crucial for getting output from the sandboxed vm.
 * @param userCode The code string provided by the AI.
 * @returns The transformed code with an explicit return.
 */
function wrapUserCode(userCode: string): string {
  const project = new Project({ useInMemoryFileSystem: true });
  const sourceFile = project.createSourceFile("userCode.ts", userCode);
  const lastStatement = sourceFile.getStatements().pop();

  if (lastStatement && lastStatement.getKind() === SyntaxKind.ExpressionStatement) {
    const returnStatement = lastStatement.asKindOrThrow(SyntaxKind.ExpressionStatement);
    const expression = returnStatement.getExpression();
    // Replace the last expression statement with a return statement
    returnStatement.replaceWithText(`return ${expression.getText()};`);
  }

  return sourceFile.getFullText();
}

/**
 * Registers the generic `run-gcp-code` tool with the MCP server.
 * @param server The MCP server instance.
 */
export function registerGenericTool(server: McpServer): void {
  server.tool(
    'run-gcp-code',
    {
      reasoning: z.string().describe("The reasoning behind the code you are about to write."),
      code: z.string().describe(codePrompt),
    },
    async ({ reasoning, code }, _extra) => {
      try {
        const projectId = await getProjectId();
        logger.info(`Executing generic code for project: ${projectId}. Reasoning: ${reasoning}`);

        // Documentation for available clients that the AI can access via help()
        const gcpClientDocs = `
Available clients in the execution context:
- compute: new InstancesClient({ projectId })
- storage: new Storage({ projectId })
- functions: new CloudFunctionsServiceClient({ projectId })
- run: new ServicesClient({ projectId })
- bigquery: new BigQuery({ projectId })
- resourceManager: new ProjectsClient({ projectId })
- billing: new CloudBillingClient()
- budgets: new BudgetServiceClient()
- container: new ClusterManagerClient()
- logging: new Logging({ projectId })
- sql: new SqlInstancesServiceClient()
- monitoring: new MetricServiceClient()
- spanner: new Spanner({ projectId })

Example: To list all GCS buckets, write the following code:
const [buckets] = await storage.getBuckets();
return buckets.map(b => b.name);
`;

        // Create a secure sandbox with all available clients
        const context = {
          projectId,
          // GCP Clients
          compute: new InstancesClient({ projectId }),
          storage: new Storage({ projectId }),
          functions: new CloudFunctionsServiceClient({ projectId }),
          run: new ServicesClient({ projectId }),
          bigquery: new BigQuery({ projectId }),
          resourceManager: new ProjectsClient({ projectId }),
          billing: new CloudBillingClient(),
          budgets: new BudgetServiceClient(),
          container: new ClusterManagerClient(),
          logging: new Logging({ projectId }),
          sql: new SqlInstancesServiceClient(),
          monitoring: new MetricServiceClient(),
          spanner: new Spanner({ projectId }),
          // Helper function
          help: () => gcpClientDocs,
        };

        const wrappedCode = wrapUserCode(code);
        // Wrap in an async IIFE to support top-level await
        const fullCode = `(async () => { ${wrappedCode} })();`;
        
        const result = await runInContext(fullCode, createContext(context));

        logger.debug(`Generic code execution successful. Result: ${JSON.stringify(result)}`);

        return {
          content: [{
            type: 'text',
            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2),
          }],
        };
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger.error(`Error executing generic GCP code: ${errorMessage}`);
        throw new GcpMcpError(
          `Error executing GCP code: ${errorMessage}`,
          'EXECUTION_FAILED',
          500
        );
      }
    }
  );
}
